"""
Django settings for config project.

Generated by 'django-admin startproject' using Django 4.2.2.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.2/ref/settings/
"""

from pathlib import Path
from decouple import config

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = config('SECRET_KEY')

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = config('DEBUG', cast=bool, default=True)

# Set allowed hosots to '*' if debug is true
if DEBUG:
    ALLOWED_HOSTS = ['*']
else:
    ALLOWED_HOSTS = [
        '.railway.app', '.ecolepro.net', 'ecolepro.net', '127.0.0.1',
    ]
    
CSRF_TRUSTED_ORIGINS = [
    'https://www.railway.app', 
    'https://www.ecolepro.net', 
    'https://ecolepro.net', 
    'https://127.0.0.1',
    'https://ecolepro.up.railway.app'
]

# Application definition

INSTALLED_APPS = [
    'grappelli',
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',

    # New
    'users',
    'school',
    'exams',
    'django_htmx',
    'widget_tweaks',
    'debug_toolbar',
    'formtools',
    'sweetify',
    'django.contrib.humanize',
    'pwa',
    'django_celery_results',
    'celery_progress',
    'import_export',
    'template_partials',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'debug_toolbar.middleware.DebugToolbarMiddleware',
    'django_htmx.middleware.HtmxMiddleware',
    'whitenoise.middleware.WhiteNoiseMiddleware',
    'config.middleware.ActiveUserMiddleware',
]

ROOT_URLCONF = 'config.urls'

default_loaders = [
    "django.template.loaders.filesystem.Loader",
    "django.template.loaders.app_directories.Loader",
]
cached_loaders = [("django.template.loaders.cached.Loader", default_loaders)]
partial_loaders = [("template_partials.loader.Loader", cached_loaders)]

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'templates'],
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        "debug": True,
        "loaders": partial_loaders,
        },
    },
]

WSGI_APPLICATION = 'config.wsgi.application'


# Database
# https://docs.djangoproject.com/en/4.2/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': config('DB_NAME'),
        'HOST': config('DB_HOST'),
        'USER': config('DB_USER'),
        'PASSWORD': config('DB_PASSWORD'),
        'PORT': config('DB_PORT'),
        'CONN_MAX_AGE': 30,
    }
}


# Password validation
# https://docs.djangoproject.com/en/4.2/ref/settings/#auth-password-validators
AUTH_USER_MODEL = 'users.CustomUser'
AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/4.2/topics/i18n/

LANGUAGE_CODE = 'fr'

TIME_ZONE = 'UTC'

USE_I18N = True

USE_TZ = True

USE_L10N = False

DATE_INPUT_FORMATS = ["%d/%m/%Y"]

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.2/howto/static-files/

STATIC_URL = 'static/'
STATICFILES_DIRS = [BASE_DIR.joinpath('static')]
STATIC_ROOT = BASE_DIR / 'staticfiles'

MEDIA_ROOT = BASE_DIR.joinpath('media')
if not DEBUG:
    MEDIA_ROOT = BASE_DIR.joinpath('/app/media')

MEDIA_URL = '/media/'
# STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'
STORAGES = {
    "default": {
        "BACKEND": 'cloudinary_storage.storage.MediaCloudinaryStorage',
    },
    "staticfiles": {
        "BACKEND": "whitenoise.storage.CompressedManifestStaticFilesStorage",
    },
}

# Default primary key field type
# https://docs.djangoproject.com/en/4.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Login and Logout
LOGIN_REDIRECT_URL = 'school:home'
LOGOUT_REDIRECT_URL = 'users:login'

# Debug Toolbar
INTERNAL_IPS = [
    "127.0.0.1",
]

# Sessions
SESSION_COOKIE_AGE = (60 * 60 * 60 * 2) + 2 # Expires in 2 hours 2 minutes

# Sweetify
SWEETIFY_SWEETALERT_LIBRARY = 'sweetalert2'

# Cache
if not DEBUG:
    CACHES = {
        'default': {
            'BACKEND': 'django_redis.cache.RedisCache',
            'LOCATION': f"{config('REDIS_URL')}",
            'OPTIONS': {
                'CLIENT_CLASS': 'django_redis.client.DefaultClient',
                'PASSWORD': config('REDIS_PASSWORD', default='test'),
            },
        },
    }
    CACHE_TTL = 300

# PWA
# Django PWA
PWA_APP_NAME = 'EcolePro'
PWA_APP_DESCRIPTION = "Application de Gestion des Ecoles Islamiques et Laïques en Côte d'Ivoire"
PWA_APP_THEME_COLOR = '#0A0302'
PWA_APP_BACKGROUND_COLOR = '#ffffff'
PWA_APP_DISPLAY = 'standalone'
PWA_APP_SCOPE = '/'
PWA_APP_ORIENTATION = 'any'
PWA_APP_START_URL = '/tableau-de-bord/'
PWA_APP_STATUS_BAR_COLOR = 'default'
PWA_APP_ICONS = [
    {
        'src': '/static/img/app_icon.png',
        'sizes': '348x348'
    }
]
PWA_APP_ICONS_APPLE = [
    {
        'src': '/static/img/app_icon.png',
        'sizes': '348x348'
    }
]
PWA_APP_SPLASH_SCREEN = [
    {
        'src': '/static/img/app_icon.png',
        'media': '(device-width: 320px) and (device-height: 568px) and (-webkit-device-pixel-ratio: 2)'
    }
]
PWA_APP_SCREENSHOTS = [
    {
      'src': '/static/img/app_icon.png',
      'sizes': '348x348',
      'type': 'image/png',
    }
]
PWA_APP_LANG = 'fr'

# Cloudinary
# DEFAULT_FILE_STORAGE = 'cloudinary_storage.storage.MediaCloudinaryStorage'
CLOUDINARY_STORAGE = {
   'CLOUD_NAME': config('CLOUDINARY_CLOUD_NAME'),
   'API_KEY': config('CLOUDINARY_API_KEY'),
   'API_SECRET': config('CLOUDINARY_SECRET_KEY'),
}

LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
        },
    },
    'root': {
        'handlers': ['console'],
        'level': 'ERROR',
    }
}

DATA_UPLOAD_MAX_NUMBER_FIELDS = 5000

# Celery
CELERY_BROKER_URL = config('CELERY_BROKER_URL')
CELERY_RESULT_BACKEND = "django-db"
CELERY_ACCEPT_CONTENT = ['application/json']
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'

# SMS API
SMS_API_KEY = config('SMS_API_KEY', default='sms_api_key')
SMS_API_TOKEN = config('SMS_API_TOKEN', default='sms_api_token')
SMS_SENDER_ID = config('SMS_SENDER_ID', default='sms_sender_id')

# Test account
TEST_ACCOUNT_DAYS = config('TEST_ACCOUNT_DAYS', default=30)
ADMIN_PHONE = '+*************'