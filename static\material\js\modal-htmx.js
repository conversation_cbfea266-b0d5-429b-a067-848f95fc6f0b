/**
 * Material Design Modal HTMX Integration
 * Provides automatic modal show/hide behavior similar to the old Bootstrap modal system
 * 
 * Features:
 * - Automatically shows modal when HTMX targets modal content
 * - Automatically hides modal on 204 status responses
 * - Initializes Material Design components after content swap
 * - Handles modal cleanup on hide
 */

(function() {
    'use strict';
    
    // Modal instances cache
    const modalInstances = new Map();
    
    /**
     * Get or create modal instance (always refresh to handle DOM changes)
     */
    function getModalInstance(modalId) {
        // Always refresh the modal instance to handle DOM changes after HTMX requests
        // Use generic modal IDs for consistency
        const modalOverlay = document.getElementById('modal-overlay');
        if (modalOverlay) {
            const instance = {
                overlay: modalOverlay,
                modal: modalOverlay.querySelector('.modal'),
                content: modalOverlay.querySelector('#modal-content'),
                title: modalOverlay.querySelector('#modal-title'),
                closeBtn: modalOverlay.querySelector('#modal-close')
            };
            modalInstances.set(modalId, instance);
            return instance;
        }
        return modalInstances.get(modalId);
    }
    
    /**
     * Clear modal instances cache (useful after DOM changes)
     */
    function clearModalInstancesCache() {
        modalInstances.clear();
    }

    /**
     * Show modal with animation
     */
    function showModal(modalId) {
        const instance = getModalInstance(modalId);
        if (!instance) {
            console.warn(`Modal instance not found for ${modalId}`);
            return;
        }

        instance.overlay.classList.add('active');
        document.body.style.overflow = 'hidden';

        // Focus first input after animation
        setTimeout(() => {
            const firstInput = instance.content.querySelector('input, select, textarea');
            if (firstInput) {
                firstInput.focus();
            }
        }, 300);

        console.log(`Material modal ${modalId} shown`);
    }
    
    /**
     * Hide modal with animation
     */
    function hideModal(modalId) {
        const instance = getModalInstance(modalId);
        if (!instance) return;
        
        instance.overlay.classList.remove('active');
        document.body.style.overflow = '';
        
        console.log(`Material modal ${modalId} hidden`);
    }
    
    /**
     * Clear modal content
     */
    function clearModalContent(modalId) {
        const instance = getModalInstance(modalId);
        if (!instance || !instance.content) return;
        
        // Clear content after hide animation completes
        setTimeout(() => {
            instance.content.innerHTML = '';
        }, 300);
    }
    
    /**
     * Initialize Material Design components in modal content
     */
    function initializeMaterialComponents(container) {
        if (!container || typeof mdc === 'undefined') return;
        
        // Initialize text fields
        container.querySelectorAll('.mdc-text-field').forEach(textField => {
            if (!textField.mdcTextField) {
                try {
                    textField.mdcTextField = new mdc.textField.MDCTextField(textField);
                } catch (e) {
                    console.warn('Failed to initialize MDC text field:', e);
                }
            }
        });
        
        // Initialize select fields
        container.querySelectorAll('.mdc-select').forEach(select => {
            if (!select.mdcSelect) {
                try {
                    const selectComponent = new mdc.select.MDCSelect(select);
                    select.mdcSelect = selectComponent;
                    
                    // Handle HTMX triggers on select change
                    selectComponent.listen('MDCSelect:change', () => {
                        const anchor = select.querySelector('.mdc-select__anchor');
                        if (anchor && anchor.hasAttribute('hx-get')) {
                            htmx.trigger(anchor, 'change');
                        }
                    });
                } catch (e) {
                    console.warn('Failed to initialize MDC select:', e);
                }
            }
        });
        
        // Initialize checkboxes
        container.querySelectorAll('.mdc-checkbox').forEach(checkbox => {
            if (!checkbox.mdcCheckbox) {
                try {
                    checkbox.mdcCheckbox = new mdc.checkbox.MDCCheckbox(checkbox);
                } catch (e) {
                    console.warn('Failed to initialize MDC checkbox:', e);
                }
            }
        });
        
        // Initialize radio buttons
        container.querySelectorAll('.mdc-radio').forEach(radio => {
            if (!radio.mdcRadio) {
                try {
                    radio.mdcRadio = new mdc.radio.MDCRadio(radio);
                } catch (e) {
                    console.warn('Failed to initialize MDC radio:', e);
                }
            }
        });
        
        // Initialize switches
        container.querySelectorAll('.mdc-switch').forEach(switchEl => {
            if (!switchEl.mdcSwitch) {
                try {
                    switchEl.mdcSwitch = new mdc.switchControl.MDCSwitch(switchEl);
                } catch (e) {
                    console.warn('Failed to initialize MDC switch:', e);
                }
            }
        });
        
        // Initialize data tables
        container.querySelectorAll('.mdc-data-table').forEach(dataTable => {
            if (!dataTable.mdcDataTable) {
                try {
                    dataTable.mdcDataTable = new mdc.dataTable.MDCDataTable(dataTable);
                } catch (e) {
                    console.warn('Failed to initialize MDC data table:', e);
                }
            }
        });
    }
    
    /**
     * Get modal ID from target element
     */
    function getModalIdFromTarget(targetId) {
        // Handle different modal target patterns
        // For generic modal system, always return 'modal'
        if (targetId === 'modal-content' || targetId.endsWith('-content')) {
            return 'modal';
        }
        if (targetId === 'dialog' || targetId === 'dialog-xl') {
            return 'modal';
        }
        return 'modal'; // Default to generic modal
    }
    
    // HTMX Event Handlers
    
    /**
     * Handle HTMX afterSwap - show modal when content is swapped
     */
    document.addEventListener('htmx:afterSwap', function(event) {
        const target = event.detail.target;
        if (!target) return;

        const targetId = target.id;
        const modalId = getModalIdFromTarget(targetId);

        console.log(`HTMX afterSwap: target=${targetId}, modalId=${modalId}`);

        // Check if target is a modal content area
        if (targetId === 'modal-content' || targetId.includes('modal') || targetId === 'dialog' || targetId === 'dialog-xl') {
            // Clear cache to ensure fresh modal instance
            clearModalInstancesCache();

            // Initialize Material components in the new content
            initializeMaterialComponents(target);

            // Show the modal with a small delay to ensure DOM is ready
            setTimeout(() => {
                showModal(modalId);
            }, 50);
        }
    });
    
    /**
     * Handle HTMX beforeSwap - hide modal on empty response or 204 status
     */
    document.addEventListener('htmx:beforeSwap', function(event) {
        const target = event.detail.target;
        const xhr = event.detail.xhr;
        
        if (!target) return;
        
        const targetId = target.id;
        const modalId = getModalIdFromTarget(targetId);
        const savedHeader = xhr.getResponseHeader('HX-Trigger');
        
        // Handle modal targets
        if (targetId === 'modal-content' || targetId.includes('modal') || targetId === 'dialog' || targetId === 'dialog-xl' || (savedHeader && savedHeader.includes('saved'))) {
            const statusCode = xhr.status;

            // Hide modal on 204 (No Content), 201 (Created), 205 (Reset Content), or 206 (Partial Content) responses
            // OR if the response contains a 'saved' trigger header
            if (statusCode === 204 || statusCode === 201 || statusCode === 205 || statusCode === 206 ||
                (savedHeader && savedHeader.includes('saved'))) {
                hideModal(modalId);
                clearModalContent(modalId);

                // Only prevent swap for status codes that don't have content
                if (statusCode === 204 || statusCode === 201 || statusCode === 205 || statusCode === 206) {
                    event.detail.shouldSwap = false;
                }

                // Show success message for 204
                if (statusCode === 204) {
                    // You can integrate with your notification system here
                    console.log('Action completed successfully');
                }

                // Log saved trigger
                if (savedHeader && savedHeader.includes('saved')) {
                    console.log('Saved trigger received, modal closed');
                }
            }
            
            // Hide modal on empty response
            if (!xhr.response || xhr.response.trim() === '') {
                hideModal(modalId);
                clearModalContent(modalId);
                event.detail.shouldSwap = false;
            }
        }
    });
    
    /**
     * Initialize modal close handlers
     */
            // Handle modal close buttons (exclude QR scanner modal which has its own handlers)
        document.addEventListener('click', function(event) {
            const closeBtn = event.target.closest('.modal-close, .modal-cancel');
            if (closeBtn) {
                const modal = closeBtn.closest('.modal');
                if (modal && modal.id !== 'qr-scanner-modal') {
                    const modalId = modal.id;
                    hideModal(modalId);
                    clearModalContent(modalId);
                }
            }
        });
        
        // Handle overlay clicks (exclude QR scanner overlay which has its own handler)
        document.addEventListener('click', function(event) {
            if (event.target.matches('.modal-overlay') && event.target.id !== 'qr-scanner-overlay') {
                const modalId = event.target.id.replace('-overlay', '');
                hideModal(modalId);
                clearModalContent(modalId);
            }
        });

    // Clear modal cache when modal content is updated (not all swaps)
    document.addEventListener('htmx:beforeSwap', function(event) {
        const target = event.detail.target;
        if (!target) return;

        const targetId = target.id;
        // Only clear cache for modal-related swaps
        if (targetId && (targetId === 'modal-content' || targetId.includes('modal') || targetId === 'dialog' || targetId === 'dialog-xl')) {
            clearModalInstancesCache();
        }
    });

    // Expose functions globally for external use
    window.MaterialModalHTMX = {
        show: showModal,
        hide: hideModal,
        clear: clearModalContent,
        clearCache: clearModalInstancesCache,
        initializeComponents: initializeMaterialComponents
    };
    
})();
