{% load humanize %}
{% load partials %}

{% startpartial enrollment-item %}
    <div class="student-item clickable-student-card mdc-ripple-surface"
         data-student-id="{{ enrollment.student.id }}"
         data-enrollment-id="{{ enrollment.id }}"
         data-student-name="{{ enrollment.student.get_full_name }}"
         data-student-level="{{ enrollment.level_fr|default:enrollment.generic_level_fr }}"
         data-student-matricule="{{ enrollment.student.matricule|default:enrollment.student.student_id }}"
         style="cursor: pointer;">
        <div class="student-photo" style="background-image: url('{% if enrollment.student.photo %}{{ enrollment.student.photo.url }}{% elif enrollment.is_second_cycle_fr and enrollment.student.student_id %}{{ enrollment.student.government_photo }}{% else %}{{ enrollment.student.blank_photo }}{% endif %}')"></div>
        <div class="student-info">
            <div class="student-header">
                <div class="student-name">
                    {{ enrollment.student.get_full_name }}
                </div>
                <div class="enrollment-date">{{ enrollment.created_at|naturalday }}</div>
            </div>
            <div class="student-info-row">
                <span class="info-label">Matricule:</span>
                <span class="info-content">{{ enrollment.student.student_id|default:'-' }}</span>
            </div>
            {% if school_education == 'A' %}
                <div class="student-info-row">
                    <span class="info-label">Nom arabe:</span>
                    <span class="info-content">{{ enrollment.student.full_name_ar|default:'-' }}</span>
                </div>
            {% endif %}
            <div class="student-info-row">
                <span class="info-label">Date Nais. <span class='dot-seperator'>•</span> Sexe:</span>
                <span class="info-content">
                    {{ enrollment.student.birth_date_str|default:"-" }} <span class='dot-seperator'>•</span> {{ enrollment.student.get_gender_display|default:"-" }}
                </span>
            </div>
            <div class="student-info-row">
                <span class="info-label">Classe Fran <span class='dot-seperator'>•</span> Ar:</span>
                <span class="info-content level-info">
                    {% if school_education != 'F' %}
                        {{ enrollment.level_fr|default:enrollment.generic_level_fr|default:"-" }} <span class='dot-seperator'>•</span>
                        {{ enrollment.level_ar|default:enrollment.generic_level_ar|default:"-" }}
                    {% else %}
                        {{ enrollment.level_fr|default:enrollment.generic_level_fr|default:"-" }}
                    {% endif %}
                </span>
            </div>
            <div class="student-info-row">
                <span class="info-label">Payé:</span>
                <span class="info-content payment-info">
                    <span class="amount-paid">{{ enrollment.paid|intcomma|default:"0" }} / {{ enrollment.amount|intcomma|default:"0" }}</span> <span class='dot-seperator'>•</span>
                    {% if enrollment.remaining == 0 and enrollment.amount > 0 %}<span class="payment-complete">Soldé</span>{% else %}{{ enrollment.remaining|intcomma|default:"-" }}{% endif %}
                </span>
            </div>
        </div>
        <!-- <div class="student-actions">
            <button class="edit-btn" title="Modifier l'élève"
                    hx-get="{% url 'school:student_edit' enrollment.pk %}"
                    hx-target="#dialog">
                <span class="material-icons">edit</span>
            </button>
            <button class="view-btn" title="Voir détails"
                    hx-get="{% url 'school:student_detail' enrollment.pk %}"
                    hx-target="#app-content">
                <span class="material-icons">visibility</span>
            </button>
        </div> -->
    </div>
{% endpartial %}

{% for enrollment in enrollments %}
    {% partial enrollment-item %}
{% endfor %}
