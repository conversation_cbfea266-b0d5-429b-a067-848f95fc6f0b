
{% load humanize %}
{% load partials %}
{% startpartial enrollment-item %}
    <tr class="mdc-data-table__row table-row" data-item-id="{{ enrollment.id }}">
        <td class="mdc-data-table__cell mdc-data-table__cell--checkbox">
            <div class="mdc-checkbox mdc-data-table__row-checkbox">
                <input type="checkbox" class="mdc-checkbox__native-control row-checkbox" aria-label="Sélectionner la ligne">
                <div class="mdc-checkbox__background">
                    <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                        <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
                    </svg>
                    <div class="mdc-checkbox__mixedmark"></div>
                </div>
                <div class="mdc-checkbox__ripple"></div>
            </div>
        </td>
        <td class="mdc-data-table__cell">
            <div class="student-photo" style="background-image: url('{% if enrollment.student.photo %}{{ enrollment.student.photo.url }}{% elif enrollment.is_second_cycle_fr and enrollment.student.student_id %}{{ enrollment.student.government_photo }}{% else %}{{ enrollment.student.blank_photo }}{% endif %}')"></div>
        </td>
        <th class="mdc-data-table__cell" scope="row">
            <div>{{ enrollment.student.get_full_name }}</div>
            {% if enrollment.student.full_name_ar and is_arabic_school %}
            <div class="text-muted small arabic-name">{{ enrollment.student.full_name_ar }}</div>
            {% endif %}
        </th>
        <td class="mdc-data-table__cell">{{ enrollment.student.student_id|default:'-' }}</td>
        <td class="mdc-data-table__cell">{{ enrollment.level_fr|default:'-' }}</td>
        {% if is_arabic_school %}
        <td class="mdc-data-table__cell">{{ enrollment.level_ar|default:'-' }}</td>
        {% endif %}
        <td class="mdc-data-table__cell">{{ enrollment.student.gender }}</td>
        <td class="mdc-data-table__cell">
            <span class="status-badge {% if enrollment.active %}status-active{% else %}status-inactive{% endif %}">
                {{ enrollment.status|default:'Actif' }}
            </span>
        </td>
        <td class="mdc-data-table__cell numeric-cell amount-negative">{{ enrollment.debt|intcomma|default:'-' }}</td>
        <td class="mdc-data-table__cell">{{ enrollment.created_at|naturalday }}</td>
        <td class="mdc-data-table__cell numeric-cell">{{ enrollment.amount|intcomma|default:'-' }}</td>
        <td class="mdc-data-table__cell numeric-cell {% if enrollment.paid > 0 %}amount-positive{% endif %}">{{ enrollment.paid|intcomma|default:'-' }}</td>
        <td class="mdc-data-table__cell numeric-cell {% if enrollment.remaining == 0 and enrollment.amount > 0 %}amount-positive{% elif enrollment.remaining > 0 %}amount-warning{% endif %}">
            {% if enrollment.remaining == 0 and enrollment.amount > 0 %}
                Soldé
            {% else %}
                {{ enrollment.remaining|intcomma|default:'-' }}
            {% endif %}
        </td>
        <td class="mdc-data-table__cell">
            <div class="table-actions">
                <button class="action-btn edit-btn" title="Modifier l'élève" hx-get="{% url 'school:student_edit' enrollment.id %}?page={{ page }}"
                    hx-target="#modal-content">
                    <span class="material-icons">edit</span>
                </button>
                <button class="action-btn payment-btn" title="Ajouter/Modifier un paiement">
                    <span class="material-icons">payments</span>
                </button>
                <button class="action-btn delete-btn" title="Supprimer l'élève">
                    <span class="material-icons">delete</span>
                </button>
                <button class="action-btn more-btn" title="Plus d'options">
                    <span class="material-icons">more_vert</span>
                </button>
            </div>
        </td>
    </tr>
{% endpartial %}

{% for enrollment in enrollments %}
    {% partial enrollment-item %}
{% empty %}
    <tr class="mdc-data-table__row">
        <td class="mdc-data-table__cell" colspan="14" style="text-align: center; padding: 48px;">
            <div style="color: #757575;">
                <span class="material-icons" style="font-size: 48px; margin-bottom: 16px; display: block;">search_off</span>
                Aucun élève trouvé
            </div>
        </td>
    </tr>
{% endfor %}